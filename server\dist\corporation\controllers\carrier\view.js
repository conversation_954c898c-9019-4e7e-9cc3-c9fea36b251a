"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewCarrierByClient = exports.viewCarrier = void 0;
const helpers_1 = require("../../../utils/helpers");
const sortHelper_1 = require("../../../utils/sortHelper");
const viewCarrier = async (req, res) => {
    try {
        const { page, pageSize, CarrierName, VAPId, CarrierName2, notIncludeWorkReport, sortBy = "id", order = "desc", } = req.query;
        const take = Number(pageSize);
        const skip = (Number(page) - 1) * Number(pageSize);
        const searchConditions = [];
        if (CarrierName) {
            const carrierList = CarrierName.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: carrierList.map((carrierName) => ({
                    name: {
                        contains: carrierName,
                        mode: "insensitive",
                    },
                })),
            });
        }
        // if (ClientName) {
        //   const clientList = ClientName.split(",").map((item) => item.trim());
        //   searchConditions.push({
        //     OR: clientList.map((clientName) => ({
        //       client_name: {
        //         contains: clientName,
        //         mode: "insensitive",
        //       },
        //     })),
        //   });
        // }
        if (VAPId) {
            const carrierList = VAPId.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: carrierList.map((carrier) => ({
                    carrier_code: {
                        contains: carrier,
                        mode: "insensitive",
                    },
                })),
            });
        }
        if (CarrierName2) {
            const carrierList = CarrierName2.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: carrierList.map((carrier) => ({
                    carrier_2nd_name: {
                        contains: carrier,
                        mode: "insensitive",
                    },
                })),
            });
        }
        const whereClause = {
            AND: [],
        };
        if (searchConditions.length > 0) {
            whereClause.AND.push({
                AND: searchConditions,
            });
        }
        // Define allowed fields for sorting
        const allowedFields = [
            "id",
            "name",
            "carrier_code",
            "code",
            "country",
            "carrier_2nd_name",
            "created_at",
            "updated_at",
            "corporation_id",
        ];
        // Prepare sort arrays
        const sortByArr = typeof sortBy === "string" ? sortBy.split(",").map(s => s.trim()) : [String(sortBy)];
        const orderArr = typeof order === "string" ? order.split(",").map(s => s.trim()) : [String(order)];
        // Get orderBy using sortHelper
        const orderBy = (0, sortHelper_1.getOrderBy)(sortByArr, orderArr, allowedFields, "id", "desc");
        const select = notIncludeWorkReport === "true"
            ? {
                id: true,
                name: true,
                carrier_2nd_name: true,
            }
            : {
                id: true,
                name: true,
                carrier_code: true,
                code: true,
                country: true,
                carrier_2nd_name: true,
                created_at: true,
                updated_at: true,
                corporation_id: true,
                WorkReport: true,
            };
        const data = await prisma.carrier.findMany({
            where: whereClause,
            take: page ? take : undefined,
            skip: page ? skip : undefined,
            select,
            orderBy,
        });
        const datalength = await prisma.carrier.count({
            where: whereClause,
        });
        if (data) {
            return res.status(200).json({ data, datalength });
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewCarrier = viewCarrier;
const viewCarrierByClient = async (req, res) => {
    const { id } = req.params;
    try {
        const data = await prisma.clientCarrier.findMany({
            where: {
                client_id: Number(id),
            },
            include: {
                carrier: true,
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewCarrierByClient = viewCarrierByClient;
//# sourceMappingURL=view.js.map