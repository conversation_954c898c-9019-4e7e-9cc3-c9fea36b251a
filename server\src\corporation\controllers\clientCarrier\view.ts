import { handleError } from "../../../utils/helpers";
import { getOrderBy } from "../../../utils/sortHelper";

export const viewClientCarrier = async (req, res) => {
  try {
    const { sortBy = "id", order = "desc" } = req.query;

    // Define allowed fields for sorting
    const allowedFields = ["id", "client_id", "carrier_id"];

    // Prepare sort arrays
    const sortByArr = typeof sortBy === "string" ? sortBy.split(",").map(s => s.trim()) : [String(sortBy)];
    const orderArr = typeof order === "string" ? order.split(",").map(s => s.trim()) : [String(order)];

    // Get orderBy using sortHelper
    const orderBy = getOrderBy(sortByArr, orderArr, allowedFields, "id", "desc");

    const data = await prisma.clientCarrier.findMany({
      include: {
        client: true,
        carrier: true,
      },
      orderBy,
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewClientCarrierById = async (req, res) => {
  const { id } = req.params;
  const { sortBy = "id", order = "desc" } = req.query;

  try {
    // Define allowed fields for sorting
    const allowedFields = ["id", "client_id", "carrier_id"];

    // Prepare sort arrays
    const sortByArr = typeof sortBy === "string" ? sortBy.split(",").map(s => s.trim()) : [String(sortBy)];
    const orderArr = typeof order === "string" ? order.split(",").map(s => s.trim()) : [String(order)];

    // Get orderBy using sortHelper
    const orderBy = getOrderBy(sortByArr, orderArr, allowedFields, "id", "desc");

    const data = await prisma.clientCarrier.findMany({
      where: {
        client_id : Number(id),
      },
      include: {
        client: true,
        carrier: true,
      },
      orderBy,
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};
